import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';

/**
 * 音频录制服务类
 */
export class AudioRecordingService {
  private static recording: Audio.Recording | null = null;
  private static audioUri: string | null = null;

  /**
   * 请求录音权限
   * @returns Promise<boolean> 是否获得权限
   */
  static async requestPermissions(): Promise<boolean> {
    try {
      const permission = await Audio.requestPermissionsAsync();
      return permission.status === 'granted';
    } catch (error) {
      console.error('Error requesting audio permissions:', error);
      return false;
    }
  }

  /**
   * 设置音频模式
   */
  static async setAudioMode(): Promise<void> {
    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        staysActiveInBackground: false,
        shouldDuckAndroid: true,
      });
    } catch (error) {
      console.error('Error setting audio mode:', error);
      throw error;
    }
  }

  /**
   * 开始录音
   * @returns Promise<void>
   */
  static async startRecording(): Promise<void> {
    try {
      // 请求权限
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        throw new Error('需要麦克风权限才能录音');
      }

      // 设置音频模式
      await this.setAudioMode();

      // 确保之前的录音已停止
      await this.stopRecording();

      // 创建录音目录（如果不存在）
      const recordingDir = `${FileSystem.documentDirectory}recordings/`;
      const dirInfo = await FileSystem.getInfoAsync(recordingDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(recordingDir, { intermediates: true });
      }

      // 开始新录音，使用PCM格式（完全符合百度语音识别API规范）
      console.log('开始录音，使用PCM格式...');
      const { recording } = await Audio.Recording.createAsync({
        android: {
          extension: '.wav',
          outputFormat: Audio.AndroidOutputFormat.DEFAULT,
          audioEncoder: Audio.AndroidAudioEncoder.DEFAULT,
          sampleRate: 16000,
          numberOfChannels: 1,
          bitRate: 256000, // 16000 * 16 * 1 = 256000 bits/sec
        },
        ios: {
          extension: '.wav',
          outputFormat: Audio.IOSOutputFormat.LinearPCM,
          audioQuality: Audio.IOSAudioQuality.HIGH,
          sampleRate: 16000,
          numberOfChannels: 1,
          bitRate: 256000,
          linearPCMBitDepth: 16,
          linearPCMIsBigEndian: false,
          linearPCMIsFloat: false,
        },
        web: {
          mimeType: 'audio/wav',
          bitsPerSecond: 256000,
        }
      });

      console.log('录音配置:', {
        android: {
          extension: '.wav',
          outputFormat: 'DEFAULT (PCM)',
          audioEncoder: 'DEFAULT (PCM)',
          sampleRate: 16000,
          numberOfChannels: 1,
          bitRate: 256000,
        }
      });

      this.recording = recording;

      console.log('Recording started');
    } catch (error) {
      console.error('Failed to start recording:', error);
      throw error;
    }
  }

  /**
   * 停止录音
   * @returns Promise<string | null> 录音文件的URI
   */
  static async stopRecording(): Promise<string | null> {
    try {
      if (!this.recording) {
        return null;
      }

      // 停止录音
      await this.recording.stopAndUnloadAsync();

      // 获取录音URI
      const uri = this.recording.getURI();
      this.audioUri = uri;

      // 清理
      this.recording = null;

      console.log('Recording stopped, URI:', uri);
      return uri;
    } catch (error) {
      console.error('Failed to stop recording:', error);
      this.recording = null;
      return null;
    }
  }

  /**
   * 获取最近录音的URI
   * @returns string | null 录音文件的URI
   */
  static getAudioUri(): string | null {
    return this.audioUri;
  }

  /**
   * 清理录音文件
   */
  static async cleanupRecordings(): Promise<void> {
    try {
      if (this.audioUri) {
        const fileInfo = await FileSystem.getInfoAsync(this.audioUri);
        if (fileInfo.exists) {
          await FileSystem.deleteAsync(this.audioUri);
          console.log('Deleted recording file:', this.audioUri);
        }
        this.audioUri = null;
      }
    } catch (error) {
      console.error('Failed to cleanup recordings:', error);
    }
  }

  /**
   * 获取音频时长（秒）
   * @param uri 音频文件URI
   * @returns Promise<number> 音频时长（秒）
   */
  static async getAudioDuration(uri: string): Promise<number> {
    try {
      const { sound } = await Audio.Sound.createAsync({ uri });
      const status = await sound.getStatusAsync();
      await sound.unloadAsync(); // 释放资源

      return status.durationMillis ? status.durationMillis / 1000 : 0;
    } catch (error) {
      console.error('Failed to get audio duration:', error);
      return 0;
    }
  }
}
